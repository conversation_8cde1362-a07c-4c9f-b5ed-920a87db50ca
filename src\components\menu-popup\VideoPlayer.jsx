import React, { useState } from 'react'
import { useContextExperience } from '@/contexts/useContextExperience'
import { ACTIONS_EXPERIENCE_STATE } from '@/contexts/reducerExperience'

export default function VideoPlayer({data,setShowVideoPlayer}) {
  const {experienceState,disptachExperience}=useContextExperience()
  // console.log('VideoPlayer:',data)
  const [isPlaying, setIsPlaying] = useState(false);
  // State for volume level (0 to 100)
  const [volume, setVolume] = useState(50);

  // Function to toggle play/pause state
  const togglePlayPause = () => {
    setIsPlaying(!isPlaying);
  };

  // Function to handle volume change
  const handleVolumeChange = (e) => {
    setVolume(e.target.value);
  };

  const handleVideoPlayerClose = () => {
    setShowVideoPlayer(false)
  }
  
  return (
    <div className='popupWrapper flex z-10 fixed top-0 left-0 w-full h-full bg-black/50'>
        <div 
            onClick={handleVideoPlayerClose} 
            className=" flex z-10 items-center justify-center absolute top-0 right-[104px] h-[75px] w-[96px] cursor-pointer"
        >
            <div className={`rotate-45  bg-[#e7e0da] h-1 m-auto absolute w-10 duration-300 ease-linear`}/>
            <div className={`-rotate-45 bg-[#e7e0da] h-1 m-auto absolute w-10 duration-300 ease-linear`}/>
        </div>

        <div className='flex relative top-[75px] left-[75px] w-[calc(100%-75px)] h-[calc(100%-75px)] items-center justify-center overflow-y-auto overflow-x-hidden'>
            <div className="relative w-full max-w-4xl bg-gray-200 rounded-xl shadow-2xl overflow-hidden">
                {/* Video Display Area */}
                <div className="relative w-full aspect-video flex items-center justify-center">
                <ivideo
                    src={data?.url} // A placeholder image for the video content
                    alt="Video Display"
                    className="w-full h-full object-cover rounded-t-xl" // Rounded top corners
                    onError={(e) => { e.target.onerror = null; e.target.src = "https://placehold.co/1280x720/A8A29E/FFFFFF?text=Video+Load+Error"; }}
                />

                {/* Left Navigation Arrow */}
                <button className="absolute left-4 top-1/2 -translate-y-1/2 p-3 bg-white bg-opacity-30 hover:bg-opacity-50 text-white rounded-full transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-white">
                    {/* SVG for left arrow */}
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M15 19l-7-7 7-7" />
                    </svg>
                </button>

                {/* Right Navigation Arrow */}
                <button className="absolute right-4 top-1/2 -translate-y-1/2 p-3 bg-white bg-opacity-30 hover:bg-opacity-50 text-white rounded-full transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-white">
                    {/* SVG for right arrow */}
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M9 5l7 7-7 7" />
                    </svg>
                </button>
                </div>

                {/* Control Bar */}
                <div className="flex items-center justify-between p-4 bg-gray-900 text-white rounded-b-xl">
                {/* Left controls: Volume and Play/Pause */}
                <div className="flex items-center space-x-4">
                    {/* Volume Icon */}
                    <button className="p-2 hover:bg-gray-700 rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500">
                    {/* SVG for volume */}
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2">
                        <path strokeLinecap="round" strokeLinejoin="round" d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.586 15H4a1 1 0 01-1-1v-4a1 1 0 011-1h1.586l4.707-4.707C10.923 3.689 11.5 4.103 11.5 5v14c0 .897-.577 1.311-1.207.684L5.586 15z" />
                    </svg>
                    </button>

                    {/* Volume Adjuster (Slider) */}
                    <input
                    type="range"
                    min="0"
                    max="100"
                    value={volume}
                    onChange={handleVolumeChange}
                    className="w-20 h-1 bg-gray-600 rounded-lg appearance-none cursor-pointer range-sm accent-blue-500"
                    style={{
                        '--tw-ring-color': 'rgb(59 130 246)', // Equivalent to ring-blue-500
                        '--tw-accent-color': 'rgb(59 130 246)' // Custom property for accent color
                    }}
                    />


                    {/* Play/Pause Button */}
                    <button
                    onClick={togglePlayPause}
                    className="p-2 hover:bg-gray-700 rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500"
                    >
                    {/* Conditional SVG for Play or Pause */}
                    {isPlaying ? (
                        // Pause icon
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2">
                        <path strokeLinecap="round" strokeLinejoin="round" d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    ) : (
                        // Play icon
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2">
                        <path strokeLinecap="round" strokeLinejoin="round" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                        <path strokeLinecap="round" strokeLinejoin="round" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    )}
                    </button>
                </div>

                {/* Center controls: Rewind, Fast Forward, and Progress Bar */}
                <div className="flex-1 flex flex-col items-center mx-4">
                    <div className="flex items-center space-x-4 mb-2">
                    {/* Rewind Button */}
                    <button className="p-2 hover:bg-gray-700 rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500">
                        {/* SVG for rewind */}
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2">
                        <path strokeLinecap="round" strokeLinejoin="round" d="M11 19l-7-7 7-7m8 14l-7-7 7-7" />
                        </svg>
                    </button>

                    {/* Fast Forward Button */}
                    <button className="p-2 hover:bg-gray-700 rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500">
                        {/* SVG for fast forward */}
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2">
                        <path strokeLinecap="round" strokeLinejoin="round" d="M13 5l7 7-7 7M6 5l7 7-7 7" />
                        </svg>
                    </button>
                    </div>

                    {/* Progress Bar */}
                    <div className="w-full flex items-center space-x-2">
                    <span className="text-xs font-mono">00:02</span>
                    <div className="flex-1 h-1 bg-gray-700 rounded-full overflow-hidden">
                        <div className="h-full w-1/4 bg-blue-500 rounded-full"></div> {/* Example progress (25%) */}
                    </div>
                    <span className="text-xs font-mono">00:08</span>
                    </div>
                </div>

                {/* Right controls: Settings and List/Menu */}
                <div className="flex items-center space-x-4">
                    {/* Settings/Gear Icon */}
                    <button className="p-2 hover:bg-gray-700 rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500">
                    {/* SVG for settings */}
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2">
                        <path strokeLinecap="round" strokeLinejoin="round" d="M10.325 4.317c.108-.063.214-.124.32-.184a1.732 1.732 0 012.023 0c.106.06.212.121.32.184m-3.921 0a2.7 2.7 0 00-.735 1.541 2.754 2.754 0 00-.015.656 2.784 2.784 0 00-.715 1.737m0 2.219a2.7 2.7 0 00-.735 1.541 2.754 2.754 0 00-.015.656 2.784 2.784 0 00-.715 1.737m-3.921-9.458a2.7 2.7 0 01-.735 1.541 2.754 2.754 0 01-.015.656 2.784 2.784 0 01-.715 1.737m0 2.219a2.7 2.7 0 01-.735 1.541 2.754 2.754 0 01-.015.656 2.784 2.784 0 01-.715 1.737M4.317 10.325c-.063.108-.124.214-.184.32a1.732 1.732 0 000 2.023c.06.106.121.212.184.32M4.317 13.675a2.7 2.7 0 001.541.735 2.754 2.754 0 00.656.015 2.784 2.784 0 001.737.715m2.219 0a2.7 2.7 0 001.541.735 2.754 2.754 0 00.656.015 2.784 2.784 0 001.737.715M10.325 19.683c.108.063.214.124.32.184a1.732 1.732 0 002.023 0c.106-.06.212-.121.32-.184m-3.921 0a2.7 2.7 0 01-.735-1.541 2.754 2.754 0 01-.015-.656 2.784 2.784 0 01-.715-1.737m0-2.219a2.7 2.7 0 01-.735-1.541 2.754 2.754 0 01-.015-.656 2.784 2.784 0 01-.715-1.737M19.683 10.325c-.063.108-.124.214-.184.32a1.732 1.732 0 000 2.023c.06.106.121.212.184.32M19.683 13.675a2.7 2.7 0 01-1.541.735 2.754 2.754 0 01-.656.015 2.784 2.784 0 01-1.737.715m-2.219 0a2.7 2.7 0 01-1.541.735 2.754 2.754 0 01-.656.015 2.784 2.784 0 01-1.737.715M12 21.75c-1.018 0-1.954-.153-2.825-.432M12 2.25c1.018 0 1.954.153 2.825.432m-5.65 0C8.046 2.403 7.11 2.556 6.092 2.556H5.625c-.77 0-1.38.61-1.38 1.38V4.5c0 .185-.018.37-.052.552a2.7 2.7 0 01-.735 1.541C3.017 6.945 3.017 7.07 3.017 7.2c0 .185.018.37.052.552a2.7 2.7 0 01.735 1.541c.034.182.052.367.052.552v.725c0 .185-.018.37-.052.552a2.7 2.7 0 01-.735 1.541c-.034.182-.052.367-.052.552v.725c0 .185.018.37.052.552a2.7 2.7 0 01.735 1.541c.034.182.052.367.052.552V18c0 .77.61 1.38 1.38 1.38H6.092c1.018 0 1.954.153 2.825.432m5.65 0c.871-.279 1.807-.432 2.825-.432h.467c.77 0 1.38-.61 1.38-1.38v-.475c0-.185.018-.37.052-.552a2.7 2.7 0 01.735-1.541c.034-.182.052-.367.052-.552v-.725c0-.185-.018-.37-.052-.552a2.7 2.7 0 01-.735-1.541c-.034-.182-.052-.367-.052-.552v-.725c0-.185-.018-.37-.052-.552a2.7 2.7 0 01-.735-1.541c-.034-.182-.052-.367-.052-.552V4.5c0-.77-.61-1.38-1.38-1.38h-.467c-.871.279-1.807.432-2.825.432m-.75 5.625h2.25v2.25h-2.25V9h-2.25v2.25h2.25V9z" />
                    </svg>
                    </button>

                    {/* List/Menu Icon */}
                    <button className="p-2 hover:bg-gray-700 rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500">
                    {/* SVG for list/menu */}
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2">
                        <path strokeLinecap="round" strokeLinejoin="round" d="M4 6h16M4 12h16M4 18h16" />
                    </svg>
                    </button>
                </div>
                </div>
            </div>
        </div>
    </div>
  );
}
