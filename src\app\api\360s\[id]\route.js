import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import { _360Settings } from '@/models/_360Model';

// GET /api/360s/[id] - Get single 360 (no authentication required)
export async function GET(request, { params }) {
  try {
    await connectDB();
    
    const { id } = await params;
    
    const item = await _360Settings.findById(id);
    
    if (!item) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not Found',
          message: '360 not found',
        },
        { status: 404 }
      );
    }
    
    return NextResponse.json({
      success: true,
      data: item,
    });
  } catch (error) {
    console.error('Error fetching 360:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch 360',
        message: error.message,
      },
      { status: 500 }
    );
  }
}

// Shared update logic for both PUT and PATCH methods
const handleUpdate = async (request, { params }) => {
  try {
    await connectDB();

    const { id } = await params;
    const body = await request.json();

    // Debug logging for marker data persistence
    console.log('360° Update Request:', {
      id,
      hasMarkerList: !!body.markerList,
      markerCount: body.markerList?.length || 0,
      markers: body.markerList?.map(m => ({
        name: m.name,
        type: m.markerType,
        x: m.x,
        y: m.y,
        z: m.z
      })) || []
    });

    // Remove fields that shouldn't be updated directly
    delete body._id;
    delete body.createdAt;
    delete body.updatedAt;

    // Validate marker data if present
    if (body.markerList && Array.isArray(body.markerList)) {
      body.markerList = body.markerList.map(marker => ({
        name: marker.name || '',
        markerType: marker.markerType || '',
        x: Number(marker.x) || 0,
        y: Number(marker.y) || 0,
        z: Number(marker.z) || 0,
        _360Name: marker._360Name || '',
        id: marker.id || ''
      }));
    }

    const updated360 = await _360Settings.findByIdAndUpdate(
      id,
      body,
      {
        new: true,
        runValidators: true
      }
    );

    // Debug logging for successful update
    console.log('360° Update Success:', {
      id,
      savedMarkerCount: updated360.markerList?.length || 0,
      savedMarkers: updated360.markerList?.map(m => ({
        name: m.name,
        type: m.markerType,
        x: m.x,
        y: m.y,
        z: m.z
      })) || []
    });

    if (!updated360) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not Found',
          message: '360 not found',
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: updated360,
      message: '360 updated successfully',
    });
  } catch (error) {
    console.error('Error updating 360:', error);

    if (error.name === 'ValidationError') {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation Error',
          message: error.message,
          details: error.errors,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update 360',
        message: error.message,
      },
      { status: 500 }
    );
  }
};

// PUT /api/360s/[id] - Update 360 (complete replacement, no authentication required)
export async function PUT(request, { params }) {
  return handleUpdate(request, { params });
}

// PATCH /api/360s/[id] - Partial update 360 (partial updates, no authentication required)
export async function PATCH(request, { params }) {
  return handleUpdate(request, { params });
}

// DELETE /api/360s/[id] - Delete 360 (no authentication required)
export async function DELETE(request, { params }) {
  try {
    await connectDB();
    
    const { id } = await params;
    
    const deleted360 = await _360Settings.findByIdAndDelete(id);
    
    if (!deleted360) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not Found',
          message: '360 not found',
        },
        { status: 404 }
      );
    }
    
    return NextResponse.json({
      success: true,
      data: deleted360,
      message: '360 deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting 360:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to delete 360',
        message: error.message,
      },
      { status: 500 }
    );
  }
}
