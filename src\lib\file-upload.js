import { ref, uploadBytes, getDownloadURL, deleteObject } from 'firebase/storage';
import { storage } from '@/lib/firebase';

/**
 * Upload file to Firebase Storage only (simplified)
 */
export async function uploadFile(file, folder = 'general', filename = null) {
  try {
    // Generate filename if not provided
    if (!filename) {
      const timestamp = Date.now();
      const extension = file.name.split('.').pop();
      filename = `${timestamp}.${extension}`;
    }

    const filePath = `elephantisland/${folder}/${filename}`;

    console.log(`Uploading file to Firebase: ${filePath}`);

    // Upload to Firebase Storage
    const storageRef = ref(storage, filePath);
    const snapshot = await uploadBytes(storageRef, file);
    const downloadURL = await getDownloadURL(snapshot.ref);

    console.log(`Upload successful: ${downloadURL}`);

    return {
      success: true,
      url: downloadURL,
      path: filePath,
      filename,
      size: file.size,
      type: file.type
    };
  } catch (error) {
    console.error('Firebase upload error:', error);
    return {
      success: false,
      error: error.message,
    };
  }
}

/**
 * Upload multiple files to Firebase Storage
 */
export async function uploadMultipleFiles(files, folder = 'general') {
  const results = [];

  for (const file of files) {
    console.log(`Processing file: ${file.name}`);
    const result = await uploadFile(file, folder);
    results.push(result);
  }

  return results;
}

/**
 * Delete file from Firebase Storage
 */
export async function deleteFile(filePath) {
  try {
    console.log(`Deleting file from Firebase: ${filePath}`);
    const storageRef = ref(storage, filePath);
    await deleteObject(storageRef);

    console.log(`File deleted successfully: ${filePath}`);
    return { success: true };
  } catch (error) {
    console.error('Firebase deletion error:', error);
    return {
      success: false,
      error: error.message,
    };
  }
}

/**
 * Validate file type and size for 360° images
 */
export function validateFile(file, options = {}) {
  const {
    maxSize = 20 * 1024 * 1024, // 20MB default for 360° images
    allowedTypes = ['image/jpeg', 'image/png', 'image/tiff'],
  } = options;

  const errors = [];

  // Check file size
  if (file.size > maxSize) {
    errors.push(`File size must be less than ${Math.round(maxSize / 1024 / 1024)}MB`);
  }

  // Check file type
  if (!allowedTypes.includes(file.type)) {
    errors.push(`File type ${file.type} is not allowed. Allowed types: ${allowedTypes.join(', ')}`);
  }

  // Additional validation for 360° images
  if (file.name && !file.name.match(/\.(jpg|jpeg|png|tiff)$/i)) {
    errors.push('File must have a valid image extension (.jpg, .jpeg, .png, .tiff)');
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

/**
 * Create simplified 360° file upload API endpoint handler
 */
export function createFileUploadHandler(folder, options = {}) {
  return async (request) => {
    try {
      console.log(`Processing upload request for folder: ${folder}`);

      const formData = await request.formData();
      const files = formData.getAll('files');

      if (!files || files.length === 0) {
        console.error('No files provided in request');
        return new Response(
          JSON.stringify({
            success: false,
            error: 'No files provided',
          }),
          { status: 400 }
        );
      }

      console.log(`Processing ${files.length} file(s)`);
      const results = [];

      for (const file of files) {
        console.log(`Validating file: ${file.name} (${file.size} bytes)`);

        // Validate file
        const validation = validateFile(file, options);
        if (!validation.isValid) {
          console.error(`Validation failed for ${file.name}:`, validation.errors);
          results.push({
            filename: file.name,
            success: false,
            errors: validation.errors,
          });
          continue;
        }

        // Upload file to Firebase
        console.log(`Uploading ${file.name} to Firebase...`);
        const uploadResult = await uploadFile(file, folder);

        if (uploadResult.success) {
          console.log(`Upload successful for ${file.name}: ${uploadResult.url}`);
        } else {
          console.error(`Upload failed for ${file.name}:`, uploadResult.error);
        }

        results.push({
          filename: file.name,
          ...uploadResult,
        });
      }

      const successCount = results.filter(r => r.success).length;
      const failCount = results.filter(r => !r.success).length;

      console.log(`Upload completed: ${successCount} successful, ${failCount} failed`);

      return new Response(
        JSON.stringify({
          success: true,
          data: results,
          summary: {
            total: results.length,
            successful: successCount,
            failed: failCount
          }
        }),
        { status: 200 }
      );
    } catch (error) {
      console.error('File upload handler error:', error);
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message,
        }),
        { status: 500 }
      );
    }
  };
}
